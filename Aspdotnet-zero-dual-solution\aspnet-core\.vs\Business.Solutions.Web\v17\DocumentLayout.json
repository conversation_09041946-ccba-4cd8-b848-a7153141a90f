{"Version": 1, "WorkspaceRootPath": "D:\\capstone\\Aspdotnet-zero-dual-solution\\aspnet-core\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9FC37C62-2105-4D32-9724-7323B959504B}|src\\Business.Solutions.Web.Host\\Business.Solutions.Web.Host.csproj|d:\\capstone\\aspdotnet-zero-dual-solution\\aspnet-core\\src\\business.solutions.web.host\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{9FC37C62-2105-4D32-9724-7323B959504B}|src\\Business.Solutions.Web.Host\\Business.Solutions.Web.Host.csproj|solutionrelative:src\\business.solutions.web.host\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "launchSettings.json", "DocumentMoniker": "D:\\capstone\\Aspdotnet-zero-dual-solution\\aspnet-core\\src\\Business.Solutions.Web.Host\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\Business.Solutions.Web.Host\\Properties\\launchSettings.json", "ToolTip": "D:\\capstone\\Aspdotnet-zero-dual-solution\\aspnet-core\\src\\Business.Solutions.Web.Host\\Properties\\launchSettings.json", "RelativeToolTip": "src\\Business.Solutions.Web.Host\\Properties\\launchSettings.json", "ViewState": "AgIAABAAAAAAAAAAAAAmwBkAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-30T19:56:01.276Z", "EditorCaption": ""}]}]}]}