<crc640ec207abc449b2ca.ShellFlyoutLayout android:layout_width="match_parent" android:layout_height="match_parent" android:background="@android:color/background_light" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:android="http://schemas.android.com/apk/res/android"><com.google.android.material.appbar.AppBarLayout android:id="@+id/flyoutcontent_appbar" android:layout_width="match_parent" android:layout_height="wrap_content" android:background="@null" app:elevation="0dp" android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" /></crc640ec207abc449b2ca.ShellFlyoutLayout>