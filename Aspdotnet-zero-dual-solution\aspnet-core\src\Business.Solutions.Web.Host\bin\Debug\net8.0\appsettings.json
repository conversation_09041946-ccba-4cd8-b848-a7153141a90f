{"ConnectionStrings": {"Default": "Server=LBPDGHO-JCABANS;Database=ptt_dev;User Id=Dev;Password=*******;TrustServerCertificate=True;"}, "AbpZeroLicenseCode": "00OfabkG6BasDZVYnG/Irc9Q==dd93b0a50b6b28992818de2dbd14bf88", "Abp": {"RedisCache": {"ConnectionString": "localhost", "DatabaseId": -1}}, "App": {"ServerRootAddress": "https://localhost:44301/", "ClientRootAddress": "http://localhost:4200/", "CorsOrigins": "http://*.mycompany.com,http://localhost:4200,http://localhost:9876,https://************:44301,http://************:44301,http://*************:44311,https://*************:44311,http://********:44311,https://********:44311", "SwaggerEndPoint": "/swagger/v1/swagger.json", "AllowAnonymousSignalRConnection": "true", "HomePageUrl": "/index.html", "AuditLog": {"AutoDeleteExpiredLogs": {"IsEnabled": false, "ExcelBackup": {"IsEnabled": false, "FilePath": "App_Data/AuditLogsBackups/"}}}}, "Authentication": {"AllowSocialLoginSettingsPerTenant": false, "Facebook": {"IsEnabled": "false", "AppId": "", "AppSecret": ""}, "Twitter": {"IsEnabled": "false", "ApiKey": "", "ApiKeySecret": ""}, "Google": {"IsEnabled": "false", "ClientId": "", "ClientSecret": "", "UserInfoEndpoint": "https://www.googleapis.com/oauth2/v2/userinfo"}, "Microsoft": {"IsEnabled": "false", "ConsumerKey": "", "ConsumerSecret": ""}, "OpenId": {"IsEnabled": "false", "ClientId": "", "Authority": "", "LoginUrl": "", "ValidateIssuer": "false", "ResponseType": "id_token", "ClaimsMapping": [{"claim": "unique_name", "key": "preferred_username"}]}, "WsFederation": {"IsEnabled": "false", "Authority": "", "ClientId": "", "Tenant": "", "MetaDataAddress": ""}, "JwtBearer": {"IsEnabled": "true", "SecurityKey": "31CAAC9A12CC41938C4E7A5EFDC4D8A1", "Issuer": "Solutions", "Audience": "Solutions"}}, "Configuration": {"AzureKeyVault": {"IsEnabled": "false", "KeyVaultName": "", "TenantId": "", "ClientId": "", "ClientSecret": ""}}, "Twilio": {"AccountSid": "", "AuthToken": "", "SenderNumber": ""}, "Recaptcha": {"SiteKey": "6LeEZ-kUAAAAAGdgiM9BoWiRKBZOeULch73OlyZP", "SecretKey": "6LeEZ-kUAAAAADBrLM1zkIy0mF_4rbBp3toA6zIJ"}, "OpenIddict": {"IsEnabled": "false", "Applications": [{"ClientId": "client", "ClientSecret": "def2edf7-5d42-4edc-a84a-30136c340e13", "DisplayName": "Solutions_App", "ConsentType": "Explicit", "RedirectUris": ["https://oauthdebugger.com/debug"], "PostLogoutRedirectUris": [], "Scopes": ["default-api", "profile"], "Permissions": ["ept:token", "ept:authorization", "gt:password", "gt:client_credentials", "gt:authorization_code", "rst:code", "rst:code id_token"]}]}, "Payment": {"PayPal": {"IsActive": "true", "Environment": "sandbox", "BaseUrl": "https://api.sandbox.paypal.com/v1", "ClientId": "", "ClientSecret": "", "DemoUsername": "", "DemoPassword": "", "DisabledFundings": []}, "Stripe": {"IsActive": "true", "BaseUrl": "https://api.stripe.com/v1", "SecretKey": "", "PublishableKey": "", "WebhookSecret": "", "PaymentMethodTypes": ["card"]}}, "HealthChecks": {"HealthChecksEnabled": false, "HealthChecksUI": {"HealthChecksUIEnabled": false, "HealthChecks": [{"Name": "Business.Solutions.Web.Host", "Uri": "https://localhost:44301/health"}], "EvaluationTimeOnSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}}, "KestrelServer": {"IsEnabled": false}, "Kestrel": {"Certificates": {"Default": {"Path": "certificate/localhost.pfx", "Password": "password"}}}, "Swagger": {"ShowSummaries": false}}