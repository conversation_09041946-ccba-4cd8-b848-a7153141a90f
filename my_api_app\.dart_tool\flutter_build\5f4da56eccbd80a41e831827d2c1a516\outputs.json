["D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound\\assets\\js\\async_processor.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound\\assets\\js\\tau_web.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\howler\\howler.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound_player.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound_recorder.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\flutter_sound_web\\src\\flutter_sound_stream_processor.js", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\capstone\\my_api_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]