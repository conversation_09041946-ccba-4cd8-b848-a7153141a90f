#Thu Jul 31 04:03:19 SGT 2025
base.0=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.2=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\15\\classes.dex
base.3=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
base.4=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.5=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.6=D\:\\capstone\\my_api_app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
path.0=classes.dex
path.1=12/classes.dex
path.2=15/classes.dex
path.3=8/classes.dex
path.4=0/classes.dex
path.5=1/classes.dex
path.6=6/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
