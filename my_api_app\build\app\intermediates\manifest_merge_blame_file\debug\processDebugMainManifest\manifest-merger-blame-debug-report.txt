1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_api_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:25:9-27:18
30            <action android:name="android.media.browse.MediaBrowserService" />
30-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:26:13-79
30-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:26:21-76
31        </intent>
32    </queries>
33
34    <uses-permission android:name="android.permission.BLUETOOTH" />
34-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:5-68
34-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:22-65
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:5-68
35-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:22-65
36    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
36-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:5-80
36-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:22-77
37    <uses-permission android:name="android.permission.RECORD_AUDIO" />
37-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:10:5-71
37-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:10:22-68
38    <uses-permission android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT" />
38-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:5-80
38-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:22-77
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.example.my_api_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.example.my_api_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
47        android:name="android.app.Application"
48        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
49        android:debuggable="true"
50        android:extractNativeLibs="false"
51        android:icon="@mipmap/ic_launcher"
52        android:label="my_api_app" >
53        <activity
54            android:name="com.example.my_api_app.MainActivity"
55            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
56            android:exported="true"
57            android:hardwareAccelerated="true"
58            android:launchMode="singleTop"
59            android:taskAffinity=""
60            android:theme="@style/LaunchTheme"
61            android:windowSoftInputMode="adjustResize" >
62
63            <!--
64                 Specifies an Android theme to apply to this Activity as soon as
65                 the Android process has started. This theme is visible to the user
66                 while the Flutter UI initializes. After that, this theme continues
67                 to determine the Window background behind the Flutter UI.
68            -->
69            <meta-data
70                android:name="io.flutter.embedding.android.NormalTheme"
71                android:resource="@style/NormalTheme" />
72
73            <intent-filter>
74                <action android:name="android.intent.action.MAIN" />
75
76                <category android:name="android.intent.category.LAUNCHER" />
77            </intent-filter>
78        </activity>
79        <!--
80             Don't delete the meta-data below.
81             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
82        -->
83        <meta-data
84            android:name="flutterEmbedding"
85            android:value="2" />
86
87        <uses-library
87-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
88            android:name="androidx.window.extensions"
88-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
89            android:required="false" />
89-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
90        <uses-library
90-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
91            android:name="androidx.window.sidecar"
91-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
92            android:required="false" />
92-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
93
94        <provider
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
96            android:authorities="com.example.my_api_app.androidx-startup"
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <receiver
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
107            android:name="androidx.profileinstaller.ProfileInstallReceiver"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
108            android:directBootAware="false"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
109            android:enabled="true"
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
110            android:exported="true"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
111            android:permission="android.permission.DUMP" >
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
113                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
116                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
119                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
122                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
123            </intent-filter>
124        </receiver>
125    </application>
126
127</manifest>
