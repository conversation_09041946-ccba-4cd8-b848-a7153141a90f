-- Merging decision tree log ---
application
INJECTED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:13:5-14:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8acdfa00821030f3b4040b662984d8f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8acdfa00821030f3b4040b662984d8f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_sound] D:\capstone\my_api_app\build\flutter_sound\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] D:\capstone\my_api_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] D:\capstone\my_api_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:2:1-16:12
MERGED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\510a1d4b3c054002b20ae70613be76ca\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2627ff82faf7c9862a29f780685f2d37\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195a7352f3cc33cd991a0edeb2a8cc21\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\755bc4e9a99e4d4711df37de3e1ca954\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\c68b785713c2991b4bb253c2bf20aa15\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\5bd6e33ce10f568108b1010184f03543\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c336bbe62d898772bd437febacfd59ec\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\989c7f19a3d41c5de931c4e500d95e10\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d7791e35106451a6ca456a18277bf079\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\322f5361d9894a7076b5603eb72aebf6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\35ca727a01fec2b59d9c7865f6cb70d3\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf4280cb7dc1ffd62912be1064d0d73a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ebcec4ac22226fed79e281e7f6036315\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c4ebfe20d117986cb14cbb9e17b1e14\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd18634761136650150f2cbf7cb70dd7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bfe722c427f9c7830d57c22cb0b80f5\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0127feb28b4ef2d25249e29fa4576155\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8e0fe9a6cac875b47e6138a0106511a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd3191843c708888f3ed2e1419c1f93\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e48866831197c108538cf67dc1ba0f0\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8acdfa00821030f3b4040b662984d8f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\380ecabec89009b8f73d138be85b261f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\755b953e20fe919d7056d5495c814939\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2360d3688e8556bd53ce0f77be8f87b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0c9edab1b2af4174469b41c9ec1abb9\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc7e22e0c21880a2c3c54df508bf976\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7212c56c4fa2cd6ed1aa294ffea93eb9\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
MERGED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:24:5-28:15
MERGED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:24:5-28:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from D:\capstone\my_api_app\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_sound] D:\capstone\my_api_app\build\flutter_sound\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_sound] D:\capstone\my_api_app\build\flutter_sound\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\capstone\my_api_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\capstone\my_api_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\capstone\my_api_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\capstone\my_api_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\510a1d4b3c054002b20ae70613be76ca\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\510a1d4b3c054002b20ae70613be76ca\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2627ff82faf7c9862a29f780685f2d37\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2627ff82faf7c9862a29f780685f2d37\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195a7352f3cc33cd991a0edeb2a8cc21\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\195a7352f3cc33cd991a0edeb2a8cc21\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\755bc4e9a99e4d4711df37de3e1ca954\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\755bc4e9a99e4d4711df37de3e1ca954\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\c68b785713c2991b4bb253c2bf20aa15\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\c68b785713c2991b4bb253c2bf20aa15\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\5bd6e33ce10f568108b1010184f03543\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\5bd6e33ce10f568108b1010184f03543\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c336bbe62d898772bd437febacfd59ec\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c336bbe62d898772bd437febacfd59ec\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\989c7f19a3d41c5de931c4e500d95e10\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\989c7f19a3d41c5de931c4e500d95e10\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d7791e35106451a6ca456a18277bf079\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d7791e35106451a6ca456a18277bf079\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\322f5361d9894a7076b5603eb72aebf6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\322f5361d9894a7076b5603eb72aebf6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\35ca727a01fec2b59d9c7865f6cb70d3\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\35ca727a01fec2b59d9c7865f6cb70d3\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf4280cb7dc1ffd62912be1064d0d73a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf4280cb7dc1ffd62912be1064d0d73a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ebcec4ac22226fed79e281e7f6036315\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ebcec4ac22226fed79e281e7f6036315\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c4ebfe20d117986cb14cbb9e17b1e14\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c4ebfe20d117986cb14cbb9e17b1e14\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd18634761136650150f2cbf7cb70dd7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd18634761136650150f2cbf7cb70dd7\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bfe722c427f9c7830d57c22cb0b80f5\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3bfe722c427f9c7830d57c22cb0b80f5\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0127feb28b4ef2d25249e29fa4576155\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0127feb28b4ef2d25249e29fa4576155\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8e0fe9a6cac875b47e6138a0106511a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8e0fe9a6cac875b47e6138a0106511a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd3191843c708888f3ed2e1419c1f93\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd3191843c708888f3ed2e1419c1f93\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e48866831197c108538cf67dc1ba0f0\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e48866831197c108538cf67dc1ba0f0\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8acdfa00821030f3b4040b662984d8f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8acdfa00821030f3b4040b662984d8f1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\380ecabec89009b8f73d138be85b261f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\380ecabec89009b8f73d138be85b261f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\755b953e20fe919d7056d5495c814939\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\755b953e20fe919d7056d5495c814939\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2360d3688e8556bd53ce0f77be8f87b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2360d3688e8556bd53ce0f77be8f87b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0c9edab1b2af4174469b41c9ec1abb9\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0c9edab1b2af4174469b41c9ec1abb9\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc7e22e0c21880a2c3c54df508bf976\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc7e22e0c21880a2c3c54df508bf976\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7212c56c4fa2cd6ed1aa294ffea93eb9\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7212c56c4fa2cd6ed1aa294ffea93eb9\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\capstone\my_api_app\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.BLUETOOTH
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:5-68
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:22-65
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:5-68
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:22-65
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:5-80
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:22-77
uses-permission#android.permission.RECORD_AUDIO
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:10:5-71
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:10:22-68
uses-permission#Manifest.permission.CAPTURE_AUDIO_OUTPUT
ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:5-80
	android:name
		ADDED from [com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b904593c83cc4ce4c4a0a424169b413\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:22-77
intent#action:name:android.media.browse.MediaBrowserService
ADDED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:25:9-27:18
action#android.media.browse.MediaBrowserService
ADDED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:26:13-79
	android:name
		ADDED from [androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\565b9381bd125103e947a18ff6dac539\transformed\media-1.4.1\AndroidManifest.xml:26:21-76
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b12214c8aa16dd7a30f2f6a4008ce77\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad2c465f98ec287fa2a00f80b2a3b944\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\12e04ab55cf776c1c1f82c4e572dda2b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.my_api_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.my_api_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba2a665d5a763c6420ad1385c85ecce\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e75103e28268bba69d13f44142e4a84b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
